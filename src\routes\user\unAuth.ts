import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../middlewares/validator';
import { loginUserSchema } from '../../utils/schema/user_schema';
import user from '../../controllers/user';

const routes: IRouter = Router();

// login user
routes.post(
    '/login',
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, loginUserSchema),
    user.loginUser
);



export default routes;
