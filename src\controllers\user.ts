import { NextFunction, Request, Response } from 'express';
import models from '../models';
import { Op } from 'sequelize';
import { TOKEN, httpStatusCodes } from '../utils/constants';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

class User {

    /**
     * @api {post} /v1/user/login
     * @apiName loginUser
     * @apiGroup Users
     *
     *
     * @apiSuccess {Object} Users.
     */
    async loginUser(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, phone, password, fcm_token } = req.body;

            if (!email && !phone) {
                throw new Error('Email or Phone is required');
            }

            const user = await models.users.findOne({
                where: {
                    [Op.or]: [email ? { email: email.toString().toLowerCase().trim() } : {}, phone ? { phone } : {}]
                }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (user.is_blocked || !user.is_active) {
                throw new Error(`User is blocked, please contact administrator.!!!`);
            }

            const isPasswordMatched = await bcrypt.compare(password, user.password);

            if (!isPasswordMatched) {
                throw new Error('Incorrect password');
            }

            await models.users.update({ fcm_token }, { where: { id: user.id } });

            const authToken = await jwt.sign({ id: user.id, role: 'user' }, TOKEN);

            delete user.dataValues.password;

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User loggedIn successfully`,
                data: { ...JSON.parse(JSON.stringify(user)), token: authToken }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }

}

export default new User();
