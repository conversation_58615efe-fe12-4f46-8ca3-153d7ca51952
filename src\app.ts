import express, { RequestHand<PERSON> } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import json2xls from 'json2xls';
import apiV1UnAuthRoutes from './routes/unAuthRoute';
import apiV1AuthRoutes from './routes/authRoute';
import { logger, accessSuccessLogger, accessErrorLogger } from './utils/logger';
import { tokenHandler } from './middlewares';
import path from 'path';
import { httpStatusCodes } from './utils/constants';

const app = express();

// register loggers
app.use(accessSuccessLogger);
app.use(accessErrorLogger);
app.use(express.static(path.join(__dirname, 'public')));

app.disable('x-powered-by');
app.use(express.json() as RequestHandler);
app.use(express.urlencoded({ extended: true }) as RequestHandler);
app.use(json2xls.middleware);

app.use(
    cors({
        credentials: true,
        origin: [
            'http://localhost:2807',
        ],
        methods: ['GET', 'POST', 'PUT', 'DELETE']
    })
);
app.use(cookieParser('CookieSecret'));

app.all('/*', (req, res, next) => {
    // 	// CORS headers
    res.header('Access-Control-Allow-Origin', '*'); // restrict it to the required domain
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    // Set custom headers for CORS
    res.header('Access-Control-Allow-Headers', 'Content-type,Accept,X-Access-Token,X-Key,Authorization,Client-Key');

    if (req.method === 'OPTIONS') {
        res.status(httpStatusCodes.SUCCESS_CODE).end();
    } else {
        next();
    }
});

app.use('/api/v1', apiV1UnAuthRoutes);
app.use('/api/v1/auth', tokenHandler, apiV1AuthRoutes);


// setTimeout(() => {
//     policyViolations.checkViolationThreshold();
// }, 1000)

// global error handler
// TODO: save error in one file and sending mail on error
app.use((err, req, res, next) => {
    logger.error(`!!!!!!Global Error!!!!!!! ${err.stack}`);

    res.status(500).json({
        status: 500,
        message:
            typeof err === 'string' ? err : typeof err?.message === 'string' ? err?.message : 'Internal Server Error',
        error: err
    });
});

export = app;
