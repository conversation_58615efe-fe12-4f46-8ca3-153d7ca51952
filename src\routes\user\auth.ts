import { IRouter, NextFunction, Request, Response, Router } from 'express';
import { fieldsValidator } from '../../middlewares/validator';

const routes: IRouter = Router();

// user change email
// routes.put(
//     '/change-email',
//     (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, changeEmailSchema),
//     user.changeUserEmail
// );

export default routes;
